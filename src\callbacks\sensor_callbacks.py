"""
Sensor analysis callbacks for wind sensor integrity analysis.
"""

import dash
from dash import Input, Output, State, callback, ctx, html
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go

from ..layouts.sensor_analysis import (
    create_sensor_analysis_layout, create_sensor_comparison_chart,
    create_deviation_chart, create_correlation_analysis, create_anomaly_results
)
from ..utils.helpers import detect_sensor_anomalies, calculate_wind_correlation
from ..utils.config import WIND_SPEED_DEVIATION_THRESHOLD


@callback(
    Output('sensor-analysis-panel', 'children'),
    [Input('data-store', 'data')]
)
def show_sensor_analysis_panel(data_store):
    """Show the sensor analysis panel when data is loaded."""
    if data_store:
        layout = create_sensor_analysis_layout()
        return layout
    return []


@callback(
    [Output('sensor-target-turbine', 'options'),
     Output('sensor-target-turbine', 'value')],
    [Input('data-store', 'data'),
     Input('selected-turbine-store', 'data')]
)
def update_sensor_target_options(data_store, selected_turbine):
    """Update target turbine options for sensor analysis."""
    if not data_store:
        return [], None

    try:
        # Get unique turbine IDs
        df = pd.DataFrame(data_store['data'])
        turbine_ids = sorted(df['StationId'].unique())

        options = [{'label': turbine_id, 'value': turbine_id} for turbine_id in turbine_ids]

        # Use selected turbine from main table if available, otherwise default to first
        if selected_turbine and selected_turbine in turbine_ids:
            default_value = selected_turbine
        else:
            default_value = turbine_ids[0] if turbine_ids else None

        return options, default_value

    except Exception as e:
        return [], None


@callback(
    [Output('sensor-reference-turbines', 'options'),
     Output('sensor-reference-turbines', 'value'),
     Output('sensor-reference-metmasts', 'options'),
     Output('sensor-reference-metmasts', 'value')],
    [Input('sensor-target-turbine', 'value')],
    [State('data-store', 'data')]
)
def update_sensor_reference_options(target_turbine, data_store):
    """Update reference options based on target turbine selection."""
    if not target_turbine or not data_store:
        return [], [], [], []

    try:
        # Get all turbine IDs except target
        df = pd.DataFrame(data_store['data'])
        all_turbines = sorted(df['StationId'].unique())
        reference_turbines = [t for t in all_turbines if t != target_turbine]

        turbine_options = [{'label': t, 'value': t} for t in reference_turbines]

        # Get metmast columns
        metmast_cols = [col for col in df.columns if col.startswith('met_WindSpeedRot_mean_')]
        metmast_options = []
        for col in metmast_cols:
            metmast_id = col.split('_')[-1]
            metmast_options.append({'label': f'Metmast {metmast_id}', 'value': col})

        # Auto-select first few references
        default_turbines = reference_turbines[:3] if len(reference_turbines) >= 3 else reference_turbines
        default_metmasts = metmast_cols

        return turbine_options, default_turbines, metmast_options, default_metmasts

    except Exception as e:
        return [], [], [], []


@callback(
    Output('sensor-target-info', 'children'),
    [Input('sensor-target-turbine', 'value'),
     Input('selected-turbine-store', 'data')],
    [State('filtered-data-store', 'data')]
)
def update_sensor_target_info(target_turbine, selected_turbine, filtered_data):
    """Update target turbine information display."""
    # Use the target turbine from sensor dropdown, or fall back to selected turbine
    active_turbine = target_turbine or selected_turbine

    if not active_turbine or not filtered_data:
        return "No turbine selected"

    try:
        # Get latest data for target turbine
        df = pd.DataFrame(filtered_data)
        df['TimeStamp'] = pd.to_datetime(df['TimeStamp'])

        turbine_data = df[df['StationId'] == active_turbine]
        if turbine_data.empty:
            return "No data available for selected turbine"

        latest = turbine_data.iloc[-1]

        # Add visual indicator if this turbine was selected from main table
        selection_indicator = ""
        if selected_turbine == active_turbine:
            selection_indicator = " 🎯"

        return [
            html.P([
                html.Strong(f"Target Turbine: "),
                html.Span(f"{active_turbine}{selection_indicator}")
            ], className="mb-1"),
            html.P([
                html.Strong("Current Status: "),
                html.Span(latest['state_category'])
            ], className="mb-1"),
            html.P([
                html.Strong("Wind Speed: "),
                html.Span(f"{latest['wtc_AcWindSp_mean']:.1f} m/s")
            ], className="mb-1"),
            html.P([
                html.Strong("Power Output: "),
                html.Span(f"{latest['wtc_ActPower_mean']:.1f} kW")
            ], className="mb-0")
        ]

    except Exception as e:
        return f"Error: {str(e)}"


@callback(
    [Output('sensor-comparison-chart', 'figure'),
     Output('sensor-deviation-chart', 'figure'),
     Output('sensor-correlation-analysis', 'children'),
     Output('sensor-anomaly-results', 'children')],
    [Input('run-sensor-analysis', 'n_clicks'),
     Input('sensor-24h', 'n_clicks'),
     Input('sensor-7d', 'n_clicks'),
     Input('sensor-30d', 'n_clicks')],
    [State('sensor-target-turbine', 'value'),
     State('sensor-reference-turbines', 'value'),
     State('sensor-reference-metmasts', 'value'),
     State('filtered-data-store', 'data')],
    prevent_initial_call=True
)
def run_sensor_analysis(run_clicks, btn_24h, btn_7d, btn_30d, target_turbine,
                       reference_turbines, metmast_columns, filtered_data):
    """Run comprehensive sensor analysis."""
    if not target_turbine or not filtered_data:
        return go.Figure(), go.Figure(), "No data available", "No analysis performed"

    try:
        # Convert data
        df = pd.DataFrame(filtered_data)
        df['TimeStamp'] = pd.to_datetime(df['TimeStamp'])

        # Determine time range
        triggered_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else None

        if triggered_id == 'sensor-24h':
            hours = 24
        elif triggered_id == 'sensor-7d':
            hours = 24 * 7
        elif triggered_id == 'sensor-30d':
            hours = 24 * 30
        else:
            hours = 24 * 7  # Default to 7 days

        # Filter by time range
        end_time = df['TimeStamp'].max()
        start_time = end_time - timedelta(hours=hours)
        time_filtered_df = df[df['TimeStamp'] >= start_time]

        # Get target turbine data
        target_data = time_filtered_df[time_filtered_df['StationId'] == target_turbine]

        if target_data.empty:
            return go.Figure(), go.Figure(), "No data for target turbine", "No analysis performed"

        # Get reference turbine data
        reference_data = None
        if reference_turbines:
            reference_data = time_filtered_df[time_filtered_df['StationId'].isin(reference_turbines)]

        # Get metmast data
        metmast_data = None
        if metmast_columns:
            metmast_data = time_filtered_df.set_index('TimeStamp')[metmast_columns].dropna()

        # Create comparison chart
        comparison_chart = create_sensor_comparison_chart(target_data, reference_data, metmast_data)

        # Create deviation chart
        deviation_chart = go.Figure()
        correlation_analysis = "No reference data available"
        anomaly_results = "No anomalies detected"

        if reference_data is not None and not reference_data.empty:
            # Calculate reference average
            ref_avg = reference_data.groupby('TimeStamp')['wtc_AcWindSp_mean'].mean().reset_index()

            # Create deviation chart
            deviation_chart = create_deviation_chart(target_data, ref_avg)

            # Correlation analysis
            correlation_analysis = create_correlation_analysis(target_data, reference_data)

            # Anomaly detection
            anomalies = detect_sensor_anomalies(
                target_data, reference_data, WIND_SPEED_DEVIATION_THRESHOLD
            )
            anomaly_results = create_anomaly_results(anomalies, WIND_SPEED_DEVIATION_THRESHOLD)

        return comparison_chart, deviation_chart, correlation_analysis, anomaly_results

    except Exception as e:
        error_fig = go.Figure().add_annotation(
            text=f"Error: {str(e)}", xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False
        )
        return error_fig, error_fig, f"Error: {str(e)}", f"Error: {str(e)}"


@callback(
    [Output('recursive-target-selector', 'options'),
     Output('recursive-analysis-btn', 'disabled')],
    [Input('sensor-reference-turbines', 'value')]
)
def update_recursive_options(reference_turbines):
    """Update recursive analysis options."""
    if not reference_turbines:
        return [], True

    options = [{'label': f'Analyze {turbine}', 'value': turbine} for turbine in reference_turbines]
    return options, False


@callback(
    Output('sensor-target-turbine', 'value', allow_duplicate=True),
    [Input('recursive-analysis-btn', 'n_clicks')],
    [State('recursive-target-selector', 'value')],
    prevent_initial_call=True
)
def perform_recursive_analysis(n_clicks, selected_reference):
    """Perform recursive analysis by setting a reference as the new target."""
    if n_clicks and selected_reference:
        return selected_reference
    from dash.exceptions import PreventUpdate
    raise PreventUpdate


@callback(
    [Output('sensor-statistics-table', 'columns'),
     Output('sensor-statistics-table', 'data')],
    [Input('run-sensor-analysis', 'n_clicks')],
    [State('sensor-target-turbine', 'value'),
     State('sensor-reference-turbines', 'value'),
     State('filtered-data-store', 'data')],
    prevent_initial_call=True
)
def update_sensor_statistics_table(n_clicks, target_turbine, reference_turbines, filtered_data):
    """Update detailed statistics table."""
    if not n_clicks or not target_turbine or not filtered_data:
        return [], []

    try:
        # Convert data
        df = pd.DataFrame(filtered_data)
        df['TimeStamp'] = pd.to_datetime(df['TimeStamp'])

        # Get target and reference data
        target_data = df[df['StationId'] == target_turbine]

        statistics = []

        # Target turbine stats
        target_stats = {
            'Turbine': target_turbine,
            'Type': 'Target',
            'Mean_Wind_Speed': target_data['wtc_AcWindSp_mean'].mean(),
            'Std_Wind_Speed': target_data['wtc_AcWindSp_mean'].std(),
            'Min_Wind_Speed': target_data['wtc_AcWindSp_mean'].min(),
            'Max_Wind_Speed': target_data['wtc_AcWindSp_mean'].max(),
            'Data_Points': len(target_data)
        }
        statistics.append(target_stats)

        # Reference turbine stats
        if reference_turbines:
            for ref_turbine in reference_turbines:
                ref_data = df[df['StationId'] == ref_turbine]
                if not ref_data.empty:
                    # Calculate correlation with target
                    merged = pd.merge(
                        target_data[['TimeStamp', 'wtc_AcWindSp_mean']],
                        ref_data[['TimeStamp', 'wtc_AcWindSp_mean']],
                        on='TimeStamp',
                        suffixes=('_target', '_ref')
                    )

                    correlation = 0
                    if len(merged) > 1:
                        correlation = merged['wtc_AcWindSp_mean_target'].corr(merged['wtc_AcWindSp_mean_ref'])
                        correlation = correlation if not pd.isna(correlation) else 0

                    ref_stats = {
                        'Turbine': ref_turbine,
                        'Type': 'Reference',
                        'Mean_Wind_Speed': ref_data['wtc_AcWindSp_mean'].mean(),
                        'Std_Wind_Speed': ref_data['wtc_AcWindSp_mean'].std(),
                        'Min_Wind_Speed': ref_data['wtc_AcWindSp_mean'].min(),
                        'Max_Wind_Speed': ref_data['wtc_AcWindSp_mean'].max(),
                        'Data_Points': len(ref_data),
                        'Correlation_with_Target': correlation
                    }
                    statistics.append(ref_stats)

        # Define columns
        columns = [
            {'name': 'Turbine', 'id': 'Turbine', 'type': 'text'},
            {'name': 'Type', 'id': 'Type', 'type': 'text'},
            {'name': 'Mean Wind (m/s)', 'id': 'Mean_Wind_Speed', 'type': 'numeric', 'format': {'specifier': '.2f'}},
            {'name': 'Std Wind (m/s)', 'id': 'Std_Wind_Speed', 'type': 'numeric', 'format': {'specifier': '.2f'}},
            {'name': 'Min Wind (m/s)', 'id': 'Min_Wind_Speed', 'type': 'numeric', 'format': {'specifier': '.2f'}},
            {'name': 'Max Wind (m/s)', 'id': 'Max_Wind_Speed', 'type': 'numeric', 'format': {'specifier': '.2f'}},
            {'name': 'Data Points', 'id': 'Data_Points', 'type': 'numeric'},
            {'name': 'Correlation', 'id': 'Correlation_with_Target', 'type': 'numeric', 'format': {'specifier': '.3f'}}
        ]

        return columns, statistics

    except Exception as e:
        return [], []
