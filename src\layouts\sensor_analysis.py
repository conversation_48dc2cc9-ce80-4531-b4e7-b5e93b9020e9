"""
Wind sensor integrity analysis layout.
Module 4: Wind Sensor Integrity Analysis (Advanced Feature)
"""

from dash import dcc, html, dash_table
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd

from ..utils.config import CHART_HEIGHT


def create_sensor_analysis_layout():
    """Create the wind sensor integrity analysis layout."""

    return dbc.Card(
        [
            dbc.CardHeader(
                [html.H4("Wind Sensor Integrity Analysis", className="mb-0")]
            ),
            dbc.CardBody(
                [
                    # Target Turbine Selection
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Target Turbine Selection",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    html.Label(
                                                        "Select Target Turbine:",
                                                        className="form-label",
                                                    ),
                                                    dcc.Dropdown(
                                                        id="sensor-target-turbine",
                                                        placeholder="Select turbine to analyze",
                                                    ),
                                                    html.Div(
                                                        id="sensor-target-info",
                                                        className="mt-3",
                                                    ),
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=4,
                            ),
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Reference Selection",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    html.Label(
                                                        "Reference Turbines:",
                                                        className="form-label",
                                                    ),
                                                    dcc.Dropdown(
                                                        id="sensor-reference-turbines",
                                                        multi=True,
                                                        placeholder="Select reference turbines",
                                                    ),
                                                    html.Label(
                                                        "Reference Metmasts:",
                                                        className="form-label mt-2",
                                                    ),
                                                    dcc.Dropdown(
                                                        id="sensor-reference-metmasts",
                                                        multi=True,
                                                        placeholder="Select reference metmasts",
                                                    ),
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=4,
                            ),
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Analysis Controls",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    html.Label(
                                                        "Time Range:",
                                                        className="form-label",
                                                    ),
                                                    dbc.ButtonGroup(
                                                        [
                                                            dbc.Button(
                                                                "24h",
                                                                id="sensor-24h",
                                                                size="sm",
                                                                outline=True,
                                                            ),
                                                            dbc.Button(
                                                                "7d",
                                                                id="sensor-7d",
                                                                size="sm",
                                                                outline=True,
                                                            ),
                                                            dbc.Button(
                                                                "30d",
                                                                id="sensor-30d",
                                                                size="sm",
                                                                outline=True,
                                                            ),
                                                        ],
                                                        className="w-100 mb-3",
                                                    ),
                                                    dbc.Button(
                                                        "Run Analysis",
                                                        id="run-sensor-analysis",
                                                        color="primary",
                                                        className="w-100",
                                                    ),
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=4,
                            ),
                        ],
                        className="mb-4",
                    ),
                    # Sensor Comparison Chart
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Wind Speed Comparison",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    dcc.Graph(
                                                        id="sensor-comparison-chart",
                                                        style={
                                                            "height": f"{CHART_HEIGHT}px"
                                                        },
                                                    )
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=12,
                            )
                        ],
                        className="mb-4",
                    ),
                    # Deviation Analysis
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Deviation Analysis",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    dcc.Graph(
                                                        id="sensor-deviation-chart",
                                                        style={
                                                            "height": f"{CHART_HEIGHT}px"
                                                        },
                                                    )
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=6,
                            ),
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Correlation Analysis",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    html.Div(
                                                        id="sensor-correlation-analysis"
                                                    )
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=6,
                            ),
                        ],
                        className="mb-4",
                    ),
                    # Anomaly Detection Results
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Anomaly Detection Results",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [html.Div(id="sensor-anomaly-results")]
                                            ),
                                        ]
                                    )
                                ],
                                width=6,
                            ),
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Recursive Analysis",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    html.P(
                                                        "Select a reference turbine to analyze as a new target:",
                                                        className="text-muted",
                                                    ),
                                                    dcc.Dropdown(
                                                        id="recursive-target-selector",
                                                        placeholder="Select reference to investigate",
                                                    ),
                                                    dbc.Button(
                                                        "Analyze Selected Reference",
                                                        id="recursive-analysis-btn",
                                                        color="secondary",
                                                        className="w-100 mt-3",
                                                        disabled=True,
                                                    ),
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=6,
                            ),
                        ],
                        className="mb-4",
                    ),
                    # Detailed Statistics Table
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Card(
                                        [
                                            dbc.CardHeader(
                                                html.H5(
                                                    "Detailed Statistics",
                                                    className="mb-0",
                                                )
                                            ),
                                            dbc.CardBody(
                                                [
                                                    dash_table.DataTable(
                                                        id="sensor-statistics-table",
                                                        columns=[],
                                                        data=[],
                                                        sort_action="native",
                                                        style_cell={
                                                            "textAlign": "left",
                                                            "padding": "8px",
                                                            "fontFamily": "Arial",
                                                        },
                                                        style_header={
                                                            "backgroundColor": "rgb(230, 230, 230)",
                                                            "fontWeight": "bold",
                                                        },
                                                    )
                                                ]
                                            ),
                                        ]
                                    )
                                ],
                                width=12,
                            )
                        ]
                    ),
                ]
            ),
        ],
        className="mt-4",
    )


def create_sensor_comparison_chart(target_data, reference_data, metmast_data=None):
    """Create wind speed comparison chart for sensor analysis."""
    fig = go.Figure()

    # Target turbine
    fig.add_trace(
        go.Scatter(
            x=target_data["TimeStamp"],
            y=target_data["wtc_AcWindSp_mean"],
            mode="lines+markers",
            name="Target Turbine",
            line=dict(color="red", width=3),
            marker=dict(size=5),
        )
    )

    # Reference turbines
    if reference_data is not None and not reference_data.empty:
        for station_id in reference_data["StationId"].unique():
            ref_turbine_data = reference_data[reference_data["StationId"] == station_id]
            fig.add_trace(
                go.Scatter(
                    x=ref_turbine_data["TimeStamp"],
                    y=ref_turbine_data["wtc_AcWindSp_mean"],
                    mode="lines",
                    name=f"Ref: {station_id}",
                    line=dict(width=2),
                    opacity=0.7,
                )
            )

    # Reference average
    if reference_data is not None and not reference_data.empty:
        ref_avg = (
            reference_data.groupby("TimeStamp")["wtc_AcWindSp_mean"]
            .mean()
            .reset_index()
        )
        fig.add_trace(
            go.Scatter(
                x=ref_avg["TimeStamp"],
                y=ref_avg["wtc_AcWindSp_mean"],
                mode="lines",
                name="Reference Average",
                line=dict(color="blue", width=3, dash="dash"),
            )
        )

    # Metmast data
    if metmast_data is not None:
        for col in metmast_data.columns:
            if col.startswith("met_WindSpeedRot_mean_"):
                metmast_id = col.split("_")[-1]
                fig.add_trace(
                    go.Scatter(
                        x=metmast_data.index,
                        y=metmast_data[col],
                        mode="lines",
                        name=f"Metmast {metmast_id}",
                        line=dict(dash="dot", width=2),
                    )
                )

    fig.update_layout(
        title="Wind Speed Sensor Comparison",
        xaxis_title="Time",
        yaxis_title="Wind Speed (m/s)",
        height=CHART_HEIGHT,
        hovermode="x unified",
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
    )

    return fig


def create_deviation_chart(target_data, reference_avg_data):
    """Create deviation analysis chart."""
    # Merge target and reference data
    merged = pd.merge(
        target_data[["TimeStamp", "wtc_AcWindSp_mean"]],
        reference_avg_data,
        on="TimeStamp",
        suffixes=("_target", "_reference"),
    )

    if merged.empty:
        return go.Figure().add_annotation(
            text="No overlapping data for deviation analysis",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
        )

    # Calculate deviation
    merged["deviation"] = (
        merged["wtc_AcWindSp_mean_target"] - merged["wtc_AcWindSp_mean_reference"]
    )
    merged["abs_deviation"] = abs(merged["deviation"])

    fig = make_subplots(
        rows=2,
        cols=1,
        subplot_titles=("Wind Speed Deviation", "Absolute Deviation"),
        vertical_spacing=0.1,
    )

    # Deviation plot
    fig.add_trace(
        go.Scatter(
            x=merged["TimeStamp"],
            y=merged["deviation"],
            mode="lines+markers",
            name="Deviation",
            line=dict(color="orange"),
            marker=dict(size=4),
        ),
        row=1,
        col=1,
    )

    # Add zero line
    fig.add_hline(y=0, line_dash="dash", line_color="gray", row=1, col=1)

    # Absolute deviation
    fig.add_trace(
        go.Scatter(
            x=merged["TimeStamp"],
            y=merged["abs_deviation"],
            mode="lines+markers",
            name="Absolute Deviation",
            line=dict(color="red"),
            marker=dict(size=4),
        ),
        row=2,
        col=1,
    )

    # Add threshold lines
    fig.add_hline(
        y=2.0,
        line_dash="dash",
        line_color="red",
        annotation_text="Threshold (2.0 m/s)",
        row=2,
        col=1,
    )

    fig.update_xaxes(title_text="Time", row=2, col=1)
    fig.update_yaxes(title_text="Deviation (m/s)", row=1, col=1)
    fig.update_yaxes(title_text="Abs Deviation (m/s)", row=2, col=1)

    fig.update_layout(
        title="Wind Speed Deviation Analysis", height=CHART_HEIGHT, showlegend=False
    )

    return fig


def create_correlation_analysis(target_data, reference_data):
    """Create correlation analysis content."""
    if reference_data is None or reference_data.empty:
        return html.P("No reference data available for correlation analysis")

    # Calculate correlation with each reference
    correlations = []

    for station_id in reference_data["StationId"].unique():
        ref_turbine_data = reference_data[reference_data["StationId"] == station_id]

        # Merge data
        merged = pd.merge(
            target_data[["TimeStamp", "wtc_AcWindSp_mean"]],
            ref_turbine_data[["TimeStamp", "wtc_AcWindSp_mean"]],
            on="TimeStamp",
            suffixes=("_target", "_reference"),
        )

        if len(merged) > 1:
            correlation = merged["wtc_AcWindSp_mean_target"].corr(
                merged["wtc_AcWindSp_mean_reference"]
            )
            r_squared = correlation**2 if not pd.isna(correlation) else 0
            mae = abs(
                merged["wtc_AcWindSp_mean_target"]
                - merged["wtc_AcWindSp_mean_reference"]
            ).mean()

            correlations.append(
                {
                    "reference": station_id,
                    "correlation": correlation if not pd.isna(correlation) else 0,
                    "r_squared": r_squared,
                    "mae": mae if not pd.isna(mae) else 0,
                    "data_points": len(merged),
                }
            )

    if not correlations:
        return html.P("No correlation data available")

    # Create correlation display
    correlation_content = []

    for corr in correlations:
        color = (
            "success"
            if corr["correlation"] > 0.8
            else "warning"
            if corr["correlation"] > 0.6
            else "danger"
        )

        correlation_content.append(
            dbc.Card(
                [
                    dbc.CardBody(
                        [
                            html.H6(f"vs {corr['reference']}", className="card-title"),
                            html.P(
                                [
                                    html.Strong("Correlation: "),
                                    html.Span(f"{corr['correlation']:.3f}"),
                                ],
                                className="mb-1",
                            ),
                            html.P(
                                [
                                    html.Strong("R²: "),
                                    html.Span(f"{corr['r_squared']:.3f}"),
                                ],
                                className="mb-1",
                            ),
                            html.P(
                                [
                                    html.Strong("MAE: "),
                                    html.Span(f"{corr['mae']:.2f} m/s"),
                                ],
                                className="mb-1",
                            ),
                            html.P(
                                [
                                    html.Strong("Points: "),
                                    html.Span(str(corr["data_points"])),
                                ],
                                className="mb-0 small text-muted",
                            ),
                        ]
                    )
                ],
                color=color,
                outline=True,
                className="mb-2",
            )
        )

    return correlation_content


def create_anomaly_results(anomalies, threshold=2.0):
    """Create anomaly detection results display."""
    if not anomalies:
        return dbc.Alert("No significant anomalies detected", color="success")

    # Categorize anomalies
    high_anomalies = [a for a in anomalies if a["deviation"] > threshold * 2]
    medium_anomalies = [
        a for a in anomalies if threshold < a["deviation"] <= threshold * 2
    ]

    results = []

    if high_anomalies:
        results.append(
            dbc.Alert(
                [
                    html.H6("🚨 High Severity Anomalies", className="alert-heading"),
                    html.P(
                        f"{len(high_anomalies)} instances with deviation > {threshold * 2:.1f} m/s"
                    ),
                ],
                color="danger",
            )
        )

    if medium_anomalies:
        results.append(
            dbc.Alert(
                [
                    html.H6("⚠️ Medium Severity Anomalies", className="alert-heading"),
                    html.P(
                        f"{len(medium_anomalies)} instances with deviation > {threshold:.1f} m/s"
                    ),
                ],
                color="warning",
            )
        )

    if not high_anomalies and not medium_anomalies:
        results.append(
            dbc.Alert(
                [
                    html.H6("✅ Minor Anomalies Only", className="alert-heading"),
                    html.P(f"{len(anomalies)} instances with minor deviations"),
                ],
                color="info",
            )
        )

    # Add summary statistics
    max_deviation = max(a["deviation"] for a in anomalies)
    avg_deviation = sum(a["deviation"] for a in anomalies) / len(anomalies)

    results.append(
        html.Div(
            [
                html.Hr(),
                html.P(
                    [
                        html.Strong("Max Deviation: "),
                        html.Span(f"{max_deviation:.2f} m/s"),
                    ]
                ),
                html.P(
                    [
                        html.Strong("Avg Deviation: "),
                        html.Span(f"{avg_deviation:.2f} m/s"),
                    ]
                ),
            ]
        )
    )

    return results
